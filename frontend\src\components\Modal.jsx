import React, { useEffect, useRef, useState } from "react";
import { useSummary } from "../context/SummaryContext";
import { uploadFiles } from "../config/api";
import Spinner from "./Spinner";

const Modal = ({ buttonText, uploadType }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [files, setFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const trigger = useRef(null);
  const modal = useRef(null);
  const { updateSummary } = useSummary();

  useEffect(() => {
    const clickHandler = (event) => {
      if (
        modal.current &&
        !modal.current.contains(event.target) &&
        trigger.current &&
        !trigger.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", clickHandler);
    return () => document.removeEventListener("mousedown", clickHandler);
  }, []);

  useEffect(() => {
    const keyHandler = (event) => {
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  }, []);

  const handleFileChange = (event) => {
    event.preventDefault();
    const newFiles = Array.from(event.target.files);
    setFiles([...files, ...newFiles]);
  };

  const removeFile = (index) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    if (files.length === 0) {
      setErrorMessage("Please select files to upload");
      return;
    }

    setIsLoading(true);
    setErrorMessage('');
    setIsOpen(false);

    try {
      const data = await uploadFiles(files, uploadType);

      console.log('Upload response:', data);
      updateSummary(
        uploadType.toLowerCase().startsWith('financ') ? 'financial' : 'proposal',
        data
      );
      setFiles([]);
    } catch (error) {
      console.error("Error uploading file:", error);

      let errorMsg = "An unexpected error occurred";

      if (error.isRateLimit) {
        errorMsg = "⚠️ Rate limit exceeded. The API has reached its usage limit. Please wait 2 minutes before trying again.";
      } else if (error.status === 400) {
        errorMsg = `❌ Upload Error: ${error.message}`;
      } else if (error.status === 500) {
        errorMsg = `🔧 Server Error: ${error.message}`;
      } else {
        errorMsg = `❌ Error: ${error.message}`;
      }

      setErrorMessage(errorMsg);
      alert(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative">
      <div className="flex flex-col items-center">
        <button
          ref={trigger}
          onClick={() => setIsOpen(true)}
          className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
          disabled={isLoading}
        >
          {buttonText}
        </button>
        {isLoading && (
          <div className="mt-2">
            <Spinner />
          </div>
        )}
        {errorMessage && !isOpen && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md max-w-xs">
            <p className="text-xs text-red-800 whitespace-pre-line">{errorMessage}</p>
            <button
              onClick={() => setErrorMessage('')}
              className="text-xs text-blue-600 hover:text-blue-800 mt-1"
            >
              Dismiss
            </button>
          </div>
        )}
      </div>

      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0 backdrop-blur-sm bg-white/30"></div>
          <div
            ref={modal}
            className="relative w-3/5 rounded-lg bg-white px-8 py-12 text-center shadow-lg"
          >
            <h3 className="pb-4 text-xl font-semibold text-gray-800 sm:text-2xl">
              Upload {uploadType} Document
            </h3>
            <span className="mx-auto mb-6 block h-1 w-24 rounded bg-primary"></span>
            
            <form onSubmit={handleUpload} className="space-y-4">
              <div>
                <label className="block text-sm font-bold text-gray-600">
                  Attach Document
                </label>
                <div className="flex items-center justify-center w-full">
                  <label className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-indigo-500">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <svg className="w-10 h-10 mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                      </svg>
                      <p className="mb-2 text-sm text-gray-500">
                        <span className="font-semibold">Click to upload</span> or drag and drop
                      </p>
                      <p className="text-xs text-gray-500">PDF and DOCX files only</p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      accept=".pdf,.docx"
                      multiple
                      onChange={handleFileChange}
                    />
                  </label>
                </div>

                <div className="mt-4 text-left">
                  {files.map((file, index) => (
                    <div key={index} className="flex justify-between items-center bg-gray-100 p-2 rounded-md mt-2">
                      <span className="text-sm text-gray-700">{file.name}</span>
                      <button
                        type="button"
                        className="text-red-500 text-sm"
                        onClick={() => removeFile(index)}
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {errorMessage && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-800 whitespace-pre-line">{errorMessage}</p>
                    </div>
                    <div className="ml-auto pl-3">
                      <button
                        type="button"
                        onClick={() => setErrorMessage('')}
                        className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100"
                      >
                        <svg className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              )}

              <div className="mt-6 flex justify-center space-x-4">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="rounded-md border border-red-600 bg-red-600 px-6 py-3 text-white font-medium transition hover:bg-red-700"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="rounded-md border border-green-600 bg-green-600 px-6 py-3 text-white font-medium transition hover:bg-green-700"
                >
                  Upload {uploadType}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

const UploadCard = ({ title, uploadType, icon }) => {
  const [dragActive, setDragActive] = useState(false);
  const [files, setFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const { updateSummary } = useSummary();

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const newFiles = Array.from(e.dataTransfer.files);
      const validFiles = newFiles.filter(file => {
        const extension = file.name.toLowerCase().split('.').pop();
        return extension === 'pdf' || extension === 'docx';
      });
      
      if (validFiles.length > 0) {
        setFiles([...files, ...validFiles]);
        handleUpload(validFiles);
      }
    }
  };

  const handleFileChange = (event) => {
    event.preventDefault();
    const newFiles = Array.from(event.target.files);
    setFiles([...files, ...newFiles]);
    if (newFiles.length > 0) {
      handleUpload(newFiles);
    }
  };

  const handleUpload = async (filesToUpload) => {
    setIsLoading(true);
    setErrorMessage('');

    try {
      const data = await uploadFiles(filesToUpload, uploadType);
      console.log('Upload response:', data);
      updateSummary(
        uploadType.toLowerCase().startsWith('financ') ? 'financial' : 'proposal',
        data
      );
      setFiles([]);
    } catch (error) {
      console.error("Error uploading file:", error);

      let errorMsg = "An unexpected error occurred";

      if (error.isRateLimit) {
        errorMsg = "⚠️ Rate limit exceeded. The API has reached its usage limit. Please wait 2 minutes before trying again.";
      } else if (error.status === 400) {
        errorMsg = `❌ Upload Error: ${error.message}`;
      } else if (error.status === 500) {
        errorMsg = `🔧 Server Error: ${error.message}`;
      } else {
        errorMsg = `❌ Error: ${error.message}`;
      }

      setErrorMessage(errorMsg);
      alert(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start space-x-3 mb-6">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
            {icon}
          </div>
        </div>
        <div className="min-w-0 flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">{title}</h3>
          <p className="text-sm text-gray-500">Upload {uploadType.toLowerCase()} forms and financial statements</p>
        </div>
      </div>

      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive 
            ? 'border-indigo-500 bg-indigo-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
            <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
            </svg>
          </div>
          <h4 className="text-base font-medium text-gray-900 mb-2">Drag & drop your files here</h4>
          <p className="text-sm text-gray-500 mb-4">or use the upload button below (PDF, DOCX supported)</p>
          {isLoading && (
            <div className="mt-2">
              <Spinner />
            </div>
          )}
        </div>
        <input
          id={`file-input-${uploadType}`}
          type="file"
          className="hidden"
          accept=".pdf,.docx"
          multiple
          onChange={handleFileChange}
        />
      </div>

      {errorMessage && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>  
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 whitespace-pre-line">{errorMessage}</p>
            </div>
            <div className="ml-auto pl-3">
              <button
                type="button"
                onClick={() => setErrorMessage('')}
                className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100"
              >
                <svg className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mt-4 flex justify-center">
        <Modal buttonText="Upload Files" uploadType={uploadType} />
      </div>
    </div>
  );
};

const FormBody = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <UploadCard 
            title="Upload Documents" 
            uploadType="Proposal"
            icon={
              <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            }
          />
          <UploadCard 
            title="Upload Documents" 
            uploadType="Financial"
            icon={
              <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            }
          />
        </div>
      </div>
    </div>
  );
};

const App = () => {
  return <FormBody />;
};

export default App;