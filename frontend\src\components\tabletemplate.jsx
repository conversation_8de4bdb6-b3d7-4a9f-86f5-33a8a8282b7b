import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Edit, Eye, Download, FileText } from 'lucide-react';

const ImprovedTableUI = () => {
  const [expandedSections, setExpandedSections] = useState({
    summary: true,
    request: true,
    analysis: true,
    investment: true
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const summaryData = [
    { label: 'Bank', value: 'Standard Bank' },
    { label: 'Branch', value: 'Nairobi Central' },
    { label: 'Account Number', value: '********90' },
    { label: 'Application Date', value: '15/07/2025' },
    { label: 'Amount Requested', value: 'KES 2,500,000' },
    { label: 'Status', value: 'Under Review', status: 'pending' }
  ];

  const requestData = [
    { label: 'Loan Type', value: 'Business Loan' },
    { label: 'Purpose', value: 'Equipment Purchase' },
    { label: 'Loan Amount', value: 'KES 2,500,000' },
    { label: 'Repayment Period', value: '36 months' },
    { label: 'Interest Rate', value: '12% p.a.' },
    { label: 'Monthly Payment', value: 'KES 83,333' },
    { label: 'Collateral', value: 'Property Title Deed' },
    { label: 'Guarantor', value: 'John Doe (ID: ********)' }
  ];

  const analysisData = [
    { label: 'Credit Score', value: '720', status: 'good' },
    { label: 'Debt-to-Income Ratio', value: '35%', status: 'good' },
    { label: 'Monthly Income', value: 'KES 150,000' },
    { label: 'Monthly Expenses', value: 'KES 85,000' },
    { label: 'Net Worth', value: 'KES 5,200,000' },
    { label: 'Employment Status', value: 'Self-employed' },
    { label: 'Years in Business', value: '8 years' },
    { label: 'Risk Assessment', value: 'Medium Risk', status: 'warning' }
  ];

  const investmentData = [
    { label: 'Investment Amount', value: 'KES 800,000' },
    { label: 'Investment Type', value: 'Equipment & Machinery' },
    { label: 'Expected ROI', value: '18% annually' },
    { label: 'Break-even Period', value: '24 months' },
    { label: 'Market Analysis', value: 'Favorable' },
    { label: 'Competition Level', value: 'Moderate' }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'pending': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const SectionHeader = ({ title, expanded, onToggle, icon }) => (
    <div 
      className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white cursor-pointer hover:from-blue-700 hover:to-blue-800 transition-all duration-200"
      onClick={onToggle}
    >
      <div className="flex items-center space-x-2">
        {icon}
        <h3 className="font-semibold text-lg">{title}</h3>
      </div>
      {expanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
    </div>
  );

  const DataRow = ({ label, value, status }) => (
    <div className="flex items-center justify-between py-3 px-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150">
      <span className="text-gray-700 font-medium">{label}</span>
      <div className="flex items-center space-x-2">
        {status ? (
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
            {value}
          </span>
        ) : (
          <span className="text-gray-900 font-semibold">{value}</span>
        )}
      </div>
    </div>
  );

  const ActionButton = ({ icon, label, variant = 'primary', onClick }) => {
    const baseClasses = "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md";
    const variants = {
      primary: "bg-blue-600 text-white hover:bg-blue-700",
      secondary: "bg-gray-200 text-gray-700 hover:bg-gray-300",
      success: "bg-green-600 text-white hover:bg-green-700"
    };
    
    return (
      <button className={`${baseClasses} ${variants[variant]}`} onClick={onClick}>
        {icon}
        <span>{label}</span>
      </button>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-50 min-h-screen">

      {/* Financial Summary Section */}
      <div className="bg-white rounded-xl shadow-lg mb-6 overflow-hidden">
        <SectionHeader 
          title="Financial Summary" 
          expanded={expandedSections.summary}
          onToggle={() => toggleSection('summary')}
          icon={<Eye size={20} />}
        />
        {expandedSections.summary && (
          <div className="divide-y divide-gray-100">
            {summaryData.map((item, index) => (
              <DataRow key={index} {...item} />
            ))}
          </div>
        )}
      </div>

      {/* Proposal Summary Section */}
      <div className="bg-white rounded-xl shadow-lg mb-6 overflow-hidden">
        <SectionHeader 
          title="Proposal Summary" 
          expanded={expandedSections.request}
          onToggle={() => toggleSection('request')}
          icon={<Edit size={20} />}
        />
        {expandedSections.request && (
          <div className="divide-y divide-gray-100">
            {requestData.map((item, index) => (
              <DataRow key={index} {...item} />
            ))}
          </div>
        )}
      </div>

      {/* Extension Section */}
      <div className="bg-white rounded-xl shadow-lg mb-6 overflow-hidden">
        <SectionHeader 
          title="Extension" 
          expanded={expandedSections.analysis}
          onToggle={() => toggleSection('analysis')}
          icon={<Download size={20} />}
        />
        {expandedSections.analysis && (
          <div className="divide-y divide-gray-100">
            {analysisData.map((item, index) => (
              <DataRow key={index} {...item} />
            ))}
          </div>
        )}
      </div>

      {/* Insurance History Section */}
      <div className="bg-white rounded-xl shadow-lg mb-6 overflow-hidden">
        <SectionHeader 
          title="Insurance History" 
          expanded={expandedSections.investment}
          onToggle={() => toggleSection('investment')}
          icon={<Eye size={20} />}
        />
        {expandedSections.investment && (
          <div className="divide-y divide-gray-100">
            {investmentData.map((item, index) => (
              <DataRow key={index} {...item} />
            ))}
          </div>
        )}
      </div>

      {/* Edit Proposal Summary Button */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
        <div className="flex justify-end">
          <ActionButton 
            icon={<Edit size={20} />}
            label="Edit Proposal Summary"
            variant="secondary"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex flex-wrap gap-3">
          <ActionButton 
            icon={<FileText size={20} />}
            label="Generate Quotation"
            variant="primary"
          />
          <ActionButton 
            icon={<Download size={20} />}
            label="Download Excel Quotation"
            variant="success"
          />
          <ActionButton 
            icon={<Download size={20} />}
            label="Download Word Quotation"
            variant="success"
          />
        </div>
      </div>

      {/* Status Footer */}
      <div className="mt-6 text-center text-sm text-gray-500">
        Last updated: July 17, 2025 at 2:30 PM
      </div>
    </div>
  );
};

export default ImprovedTableUI;